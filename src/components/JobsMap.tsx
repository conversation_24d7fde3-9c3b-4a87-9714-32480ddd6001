import React, { useEffect, useRef, useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { getAddressService } from '../services/addressService';
import { useLocalStorage } from 'usehooks-ts';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

interface JobWithLocation {
  _id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  scheduledDate?: number;
  customer: {
    name: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  assignedUser?: {
    name: string;
    email: string;
  };
}

interface JobMapProps {
  height?: string;
  className?: string;
}

export function JobsMap({ height = '300px', className = '' }: JobMapProps) {
  const jobs = useQuery(api.jobs.getInProgressWithLocations);
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [geocodedJobs, setGeocodedJobs] = useState<Array<JobWithLocation & { coordinates: [number, number] }>>([]);
  const [lastTrafficUpdate, setLastTrafficUpdate] = useState<number>(0);

  // State management for widget collapse with localStorage persistence
  const [isMinimized, setIsMinimized] = useLocalStorage('jobmap-widget-minimized', true);

  useEffect(() => {
    // Only initialize map when not minimized
    if (isMinimized) {
      setIsLoading(false);
      return;
    }

    const initializeMap = () => {
      const apiKey = import.meta.env.VITE_MAPBOX_API_KEY;
      console.log('Mapbox API Key status:', apiKey ? 'Found' : 'Missing');

      if (!apiKey) {
        setError('Mapbox API key not configured. Please set VITE_MAPBOX_API_KEY in your environment.');
        setIsLoading(false);
        return;
      }

      try {
        mapboxgl.accessToken = apiKey;

        if (map.current) return; // Initialize map only once

        map.current = new mapboxgl.Map({
          container: mapContainer.current!,
          style: 'mapbox://styles/mapbox/streets-v12', // Streets view with live traffic
          center: [-98.5795, 39.8283], // Center of US
          zoom: 3, // Lower initial zoom for mobile
          pitch: 0, // No pitch for 2D view
          bearing: 0, // Initial rotation
          antialias: true, // Smooth rendering
          attributionControl: false, // Remove attribution for more space
        });

        map.current.on('load', () => {
          // Add traffic layer for live traffic data
          map.current.addSource('mapbox-traffic', {
            'type': 'vector',
            'url': 'mapbox://mapbox.mapbox-traffic-v1'
          });
          
          // Add traffic layer with traffic congestion colors
          map.current.addLayer({
            'id': 'traffic',
            'type': 'line',
            'source': 'mapbox-traffic',
            'source-layer': 'traffic',
            'layout': {
              'line-join': 'round',
              'line-cap': 'round'
            },
            'paint': {
              'line-color': [
                'case',
                ['==', ['get', 'congestion'], 'low'], '#22c55e',
                ['==', ['get', 'congestion'], 'moderate'], '#eab308',
                ['==', ['get', 'congestion'], 'heavy'], '#f97316',
                ['==', ['get', 'congestion'], 'severe'], '#dc2626',
                '#6b7280'
              ],
              'line-width': [
                'interpolate',
                ['linear'],
                ['zoom'],
                6, 0.3,
                12, 2,
                18, 4
              ],
              'line-opacity': [
                'interpolate',
                ['linear'],
                ['zoom'],
                6, 0.4,
                12, 0.6,
                18, 0.8
              ],
              'line-offset': [
                'interpolate',
                ['linear'],
                ['zoom'],
                6, 0,
                12, 1,
                18, 2
              ]
            }
          }, 'road-label'); // Insert before road labels so street names appear on top
          
          // Add navigation controls (no pitch control for 2D view)
          const nav = new mapboxgl.NavigationControl({
            showCompass: false,
            showZoom: true,
            visualizePitch: false
          });
          map.current.addControl(nav, 'top-right');
          
          // Add fullscreen control only on desktop
          if (window.innerWidth >= 768) {
            map.current.addControl(new mapboxgl.FullscreenControl(), 'top-right');
          }
          
          setIsLoading(false);
          setLastTrafficUpdate(Date.now());
        });

        map.current.on('error', (e: any) => {
          console.error('Map error:', e);
          setError(`Failed to load map: ${e.error?.message || 'Unknown error'}`);
          setIsLoading(false);
        });

      } catch (err) {
        console.error('Failed to initialize Mapbox:', err);
        setError(`Failed to load map library: ${err instanceof Error ? err.message : 'Unknown error'}`);
        setIsLoading(false);
      }
    };

    initializeMap();
  }, [isMinimized]);

  // Traffic update effect - refresh every 2 minutes
  useEffect(() => {
    if (!map.current || isMinimized) return;

    const updateTraffic = () => {
      const now = Date.now();
      if (now - lastTrafficUpdate >= 120000) { // 2 minutes in milliseconds
        // Remove existing traffic layer
        if (map.current.getLayer('traffic')) {
          map.current.removeLayer('traffic');
        }
        if (map.current.getSource('mapbox-traffic')) {
          map.current.removeSource('mapbox-traffic');
        }

        // Re-add traffic source and layer
        map.current.addSource('mapbox-traffic', {
          'type': 'vector',
          'url': 'mapbox://mapbox.mapbox-traffic-v1'
        });

        map.current.addLayer({
          'id': 'traffic',
          'type': 'line',
          'source': 'mapbox-traffic',
          'source-layer': 'traffic',
          'layout': {
            'line-join': 'round',
            'line-cap': 'round'
          },
          'paint': {
            'line-color': [
              'case',
              ['==', ['get', 'congestion'], 'low'], '#22c55e',
              ['==', ['get', 'congestion'], 'moderate'], '#eab308',
              ['==', ['get', 'congestion'], 'heavy'], '#f97316',
              ['==', ['get', 'congestion'], 'severe'], '#dc2626',
              '#6b7280'
            ],
            'line-width': [
              'interpolate',
              ['linear'],
              ['zoom'],
              6, 0.3,
              12, 2,
              18, 4
            ],
            'line-opacity': [
              'interpolate',
              ['linear'],
              ['zoom'],
              6, 0.4,
              12, 0.6,
              18, 0.8
            ],
            'line-offset': [
              'interpolate',
              ['linear'],
              ['zoom'],
              6, 0,
              12, 1,
              18, 2
            ]
          }
        }, 'road-label');

        setLastTrafficUpdate(now);
      }
    };

    const interval = setInterval(updateTraffic, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, [lastTrafficUpdate, isMinimized]);

  useEffect(() => {
    if (!jobs || !map.current || isLoading || isMinimized) return;

    const geocodeJobs = async () => {
      const addressService = getAddressService();
      const geocoded: Array<JobWithLocation & { coordinates: [number, number] }> = [];

      for (const job of jobs) {
        try {
          const fullAddress = `${job.customer.address}, ${job.customer.city}, ${job.customer.state} ${job.customer.zipCode}`;
          const suggestions = await addressService.searchAddresses(fullAddress, { limit: 1 });
          
          if (suggestions.length > 0) {
            geocoded.push({
              ...job,
              coordinates: suggestions[0].center,
            });
          }
        } catch (error) {
          console.error(`Failed to geocode job ${job._id}:`, error);
        }
      }

      setGeocodedJobs(geocoded);
    };

    geocodeJobs();
  }, [jobs, isLoading, isMinimized]);

  useEffect(() => {
    if (!map.current || !geocodedJobs.length || isMinimized) return;

    // Clear existing markers
    const existingMarkers = document.querySelectorAll('.job-marker');
    existingMarkers.forEach(marker => marker.remove());

    // Add markers for each job
    geocodedJobs.forEach((job) => {
      // Create marker element with responsive sizing
      const isMobile = window.innerWidth < 768;
      const markerElement = document.createElement('div');
      markerElement.className = 'job-marker';
      markerElement.style.cssText = `
        width: ${isMobile ? '32px' : '40px'};
        height: ${isMobile ? '32px' : '40px'};
        background: ${getPriorityColor(job.priority)};
        border: ${isMobile ? '2px' : '3px'} solid white;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: ${isMobile ? '12px' : '16px'};
        font-weight: bold;
        color: white;
        box-shadow: 0 4px 12px rgba(0,0,0,0.4), 0 0 0 2px rgba(255,255,255,0.8);
        transform: translateZ(0);
        transition: all 0.3s ease;
        position: relative;
      `;
      markerElement.textContent = getPriorityIcon(job.priority);
      
      // Add hover effect
      markerElement.addEventListener('mouseenter', () => {
        markerElement.style.transform = 'scale(1.2) translateZ(0)';
        markerElement.style.boxShadow = '0 6px 20px rgba(0,0,0,0.6), 0 0 0 3px rgba(255,255,255,1)';
      });
      
      markerElement.addEventListener('mouseleave', () => {
        markerElement.style.transform = 'scale(1) translateZ(0)';
        markerElement.style.boxShadow = '0 4px 12px rgba(0,0,0,0.4), 0 0 0 2px rgba(255,255,255,0.8)';
      });

      // Create popup content
      const popupContent = `
        <div class="p-3 max-w-xs">
          <h3 class="font-bold text-gray-800 mb-1">${job.title}</h3>
          <p class="text-sm text-gray-600 mb-2">${job.description}</p>
          <div class="space-y-1 text-xs">
            <div class="flex items-center gap-1">
              <span class="font-medium">Customer:</span>
              <span>${job.customer.name}</span>
            </div>
            <div class="flex items-center gap-1">
              <span class="font-medium">Address:</span>
              <span>${job.customer.address}</span>
            </div>
            <div class="flex items-center gap-1">
              <span class="font-medium">City:</span>
              <span>${job.customer.city}, ${job.customer.state}</span>
            </div>
            ${job.assignedUser ? `
              <div class="flex items-center gap-1">
                <span class="font-medium">Assigned:</span>
                <span>${job.assignedUser.name}</span>
              </div>
            ` : ''}
            <div class="flex items-center gap-1">
              <span class="font-medium">Priority:</span>
              <span class="px-2 py-1 rounded text-xs text-white" style="background: ${getPriorityColor(job.priority)}">
                ${job.priority.toUpperCase()}
              </span>
            </div>
          </div>
        </div>
      `;

      // Add marker to map
      const popup = new mapboxgl.Popup({ offset: 25 }).setHTML(popupContent);
      
      new mapboxgl.Marker(markerElement)
        .setLngLat(job.coordinates)
        .setPopup(popup)
        .addTo(map.current);
    });

    // Fit map to show all markers with 3D perspective
    if (geocodedJobs.length > 0) {
      const bounds = new mapboxgl.LngLatBounds();
      geocodedJobs.forEach(job => bounds.extend(job.coordinates));
      
      // Enhanced fit bounds with responsive 2D settings
      const isMobile = window.innerWidth < 768;
      map.current.fitBounds(bounds, { 
        padding: isMobile ? 60 : 120,
        pitch: 0, // 2D view
        bearing: 0,
        duration: isMobile ? 1000 : 2000, // Faster on mobile
        maxZoom: 12 // Prevent zooming too close to individual addresses
      });
    }
  }, [geocodedJobs, isMinimized]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'emergency': return '#dc2626'; // red-600
      case 'high': return '#ea580c'; // orange-600
      case 'medium': return '#ca8a04'; // yellow-600
      case 'low': return '#16a34a'; // green-600
      default: return '#6b7280'; // gray-500
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'emergency': return '🚨';
      case 'high': return '🔥';
      case 'medium': return '⚡';
      case 'low': return '📝';
      default: return '•';
    }
  };

  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-4xl mb-2">⚠️</div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-1">
              Map Error
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {error}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-base md:text-lg font-semibold text-gray-800 dark:text-gray-200">
            <span className="hidden sm:inline">🚗 Jobs in Progress - Live Traffic</span>
            <span className="sm:hidden">🚗 Jobs in Progress</span>
          </h2>
          <div className="flex items-center gap-2 md:gap-3">
            <div className="flex items-center gap-1 md:gap-2 text-xs md:text-sm text-gray-600 dark:text-gray-400">
              <span>🚦</span>
              <span className="hidden sm:inline">{geocodedJobs.length} active job{geocodedJobs.length !== 1 ? 's' : ''}</span>
              <span className="sm:hidden">{geocodedJobs.length}</span>
            </div>
            {/* Toggle Button */}
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title={isMinimized ? "Expand map" : "Minimize map"}
              aria-label={isMinimized ? "Expand map" : "Minimize map"}
            >
              <span className={`text-lg transition-transform duration-300 ${isMinimized ? 'rotate-180' : ''}`}>
                ⌄
              </span>
            </button>
          </div>
        </div>

        {/* Legend - Responsive - Only show when not minimized */}
        {!isMinimized && (
          <div className="flex flex-wrap items-center gap-2 md:gap-4 mt-2 text-xs">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 md:w-3 md:h-3 bg-red-600 rounded-full"></div>
              <span className="hidden sm:inline">Emergency</span>
              <span className="sm:hidden">🚨</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 md:w-3 md:h-3 bg-orange-600 rounded-full"></div>
              <span className="hidden sm:inline">High</span>
              <span className="sm:hidden">🔥</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 md:w-3 md:h-3 bg-yellow-600 rounded-full"></div>
              <span className="hidden sm:inline">Medium</span>
              <span className="sm:hidden">⚡</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 md:w-3 md:h-3 bg-green-600 rounded-full"></div>
              <span className="hidden sm:inline">Low</span>
              <span className="sm:hidden">📝</span>
            </div>
          </div>
        )}
      </div>

      {/* Map Container - Only render when not minimized */}
      {!isMinimized && (
        <div className="relative">
          <div
            ref={mapContainer}
            className="w-full rounded-b-lg h-60 md:h-80"
          />

          {isLoading && (
            <div className="absolute inset-0 bg-white dark:bg-gray-800 flex items-center justify-center rounded-b-lg">
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Loading map...</span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Minimized State - Show a collapsed placeholder */}
      {isMinimized && (
        <div className="p-4 text-center text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 rounded-b-lg">
          <div className="flex items-center justify-center gap-2 text-sm">
            <span>🗺️</span>
            <span>Map minimized - {geocodedJobs.length} job{geocodedJobs.length !== 1 ? 's' : ''} available</span>
          </div>
        </div>
      )}
    </div>
  );
}