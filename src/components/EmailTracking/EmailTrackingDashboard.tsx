import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { EmailTrackingStats, RecentEmailOpens } from "../Notifications/EmailTrackingNotifications";

interface EmailTrackingDashboardProps {
  className?: string;
}

export function EmailTrackingDashboard({ className = "" }: EmailTrackingDashboardProps) {
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d'>('7d');
  const [emailType, setEmailType] = useState<string>('');

  const analytics = useQuery(api.emailTracking.getEmailTrackingAnalytics, {
    timeRange,
    emailType: emailType || undefined,
  });

  const recentOpens = useQuery(api.emailTracking.getRecentEmailOpens, { limit: 15 });

  return (
    <div className={`space-y-6 ${className}`}>
      {/* <PERSON><PERSON> */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">📧 Email Tracking</h1>
          <p className="text-gray-600">Monitor email opens and engagement</p>
        </div>
        
        <div className="mt-4 sm:mt-0 flex space-x-3">
          {/* Time Range Filter */}
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as '24h' | '7d' | '30d')}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>

          {/* Email Type Filter */}
          <select
            value={emailType}
            onChange={(e) => setEmailType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">All Email Types</option>
            <option value="invoice">Invoice Emails</option>
            <option value="invoice_pdf">PDF Invoice Emails</option>
            <option value="notification">Notifications</option>
            <option value="reminder">Reminders</option>
          </select>
        </div>
      </div>

      {/* Analytics Overview */}
      {analytics ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm">📤</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Emails Sent</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.totalSent}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-sm">📬</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Emails Opened</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.totalOpened}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 text-sm">📊</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Open Rate</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.openRate.toFixed(1)}%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-orange-600 text-sm">🔄</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Avg Opens/Email</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.averageOpensPerEmail.toFixed(1)}</p>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="ml-4 flex-1">
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Email Tracking Stats */}
        <EmailTrackingStats />

        {/* Recent Email Opens */}
        <RecentEmailOpens />
      </div>

      {/* Detailed Email List */}
      {recentOpens && recentOpens.length > 0 && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">📋 Email Tracking Details</h3>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Recipient
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Opens
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Opened
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentOpens.map((tracking) => (
                  <tr key={tracking._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                            tracking.openedAt ? 'bg-green-100' : 'bg-gray-100'
                          }`}>
                            <span className={`text-sm ${
                              tracking.openedAt ? 'text-green-600' : 'text-gray-400'
                            }`}>
                              {tracking.emailType === 'invoice_pdf' ? '📄' : '📧'}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {tracking.invoiceData ? (
                              `Invoice ${tracking.invoiceData.invoiceNumber}`
                            ) : (
                              tracking.emailType.replace('_', ' ').toUpperCase()
                            )}
                          </div>
                          <div className="text-sm text-gray-500">
                            {tracking.emailType === 'invoice_pdf' ? 'PDF Email' : 'Standard Email'}
                          </div>
                        </div>
                      </div>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {tracking.invoiceData?.customerName || 'Unknown'}
                      </div>
                      <div className="text-sm text-gray-500">{tracking.recipientEmail}</div>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        tracking.openedAt 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {tracking.openedAt ? 'Opened' : 'Sent'}
                      </span>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {tracking.openCount}
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {tracking.lastOpenedAt ? (
                        new Date(tracking.lastOpenedAt).toLocaleString()
                      ) : tracking.openedAt ? (
                        new Date(tracking.openedAt).toLocaleString()
                      ) : (
                        'Not opened'
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
