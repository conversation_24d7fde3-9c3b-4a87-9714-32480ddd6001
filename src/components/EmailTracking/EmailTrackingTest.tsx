import React, { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "../../utils/toast";

export function EmailTrackingTest() {
  const [isTestingWebhook, setIsTestingWebhook] = useState(false);
  const [isTestingPixel, setIsTestingPixel] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);

  const recentTracking = useQuery(api.emailTracking.getRecentEmailOpens, { limit: 5 });
  const analytics = useQuery(api.emailTracking.getEmailTrackingAnalytics, { timeRange: '24h' });

  // Test webhook endpoint
  const testWebhook = async () => {
    setIsTestingWebhook(true);
    try {
      const webhookUrl = `${window.location.origin}/webhooks/email-tracking`;
      
      // Simulate a Resend webhook payload
      const testPayload = {
        type: 'email.opened',
        id: `test_webhook_${Date.now()}`,
        created_at: new Date().toISOString(),
        data: {
          email_id: `test_email_${Date.now()}`,
          to: '<EMAIL>',
          subject: 'Test Email Tracking',
        }
      };

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testPayload),
      });

      const result = {
        timestamp: new Date().toISOString(),
        type: 'webhook',
        status: response.status,
        statusText: response.statusText,
        payload: testPayload,
      };

      setTestResults(prev => [result, ...prev.slice(0, 9)]);

      if (response.ok) {
        toast.success('Webhook test successful!');
      } else {
        toast.error(`Webhook test failed: ${response.status}`);
      }
    } catch (error) {
      console.error('Webhook test error:', error);
      toast.error('Webhook test failed');
      setTestResults(prev => [{
        timestamp: new Date().toISOString(),
        type: 'webhook',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, ...prev.slice(0, 9)]);
    } finally {
      setIsTestingWebhook(false);
    }
  };

  // Test tracking pixel
  const testTrackingPixel = async () => {
    setIsTestingPixel(true);
    try {
      const trackingId = `test_pixel_${Date.now()}`;
      const pixelUrl = `${window.location.origin}/track/${trackingId}`;
      
      // Create a test tracking record first
      // Note: In a real scenario, this would be created when an email is sent
      
      // Load the tracking pixel
      const img = new Image();
      img.onload = () => {
        const result = {
          timestamp: new Date().toISOString(),
          type: 'pixel',
          status: 'success',
          trackingId,
          pixelUrl,
        };
        setTestResults(prev => [result, ...prev.slice(0, 9)]);
        toast.success('Tracking pixel loaded successfully!');
        setIsTestingPixel(false);
      };
      
      img.onerror = () => {
        const result = {
          timestamp: new Date().toISOString(),
          type: 'pixel',
          status: 'error',
          trackingId,
          pixelUrl,
          error: 'Failed to load tracking pixel',
        };
        setTestResults(prev => [result, ...prev.slice(0, 9)]);
        toast.error('Tracking pixel test failed');
        setIsTestingPixel(false);
      };
      
      img.src = pixelUrl;
    } catch (error) {
      console.error('Tracking pixel test error:', error);
      toast.error('Tracking pixel test failed');
      setIsTestingPixel(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">🧪 Email Tracking Test Suite</h2>
        <p className="text-gray-600 mb-6">
          Test the email tracking system components to ensure they're working correctly.
        </p>

        {/* Test Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <button
            onClick={testWebhook}
            disabled={isTestingWebhook}
            className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isTestingWebhook ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Testing Webhook...
              </>
            ) : (
              <>🔗 Test Webhook Endpoint</>
            )}
          </button>

          <button
            onClick={testTrackingPixel}
            disabled={isTestingPixel}
            className="flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isTestingPixel ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Testing Pixel...
              </>
            ) : (
              <>📷 Test Tracking Pixel</>
            )}
          </button>
        </div>

        {/* Current Analytics */}
        {analytics && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-gray-900 mb-2">📊 Current Analytics (24h)</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-gray-500">Emails Sent</div>
                <div className="font-semibold">{analytics.totalSent}</div>
              </div>
              <div>
                <div className="text-gray-500">Emails Opened</div>
                <div className="font-semibold">{analytics.totalOpened}</div>
              </div>
              <div>
                <div className="text-gray-500">Open Rate</div>
                <div className="font-semibold">{analytics.openRate.toFixed(1)}%</div>
              </div>
              <div>
                <div className="text-gray-500">Recent Opens</div>
                <div className="font-semibold">{analytics.recentOpens}</div>
              </div>
            </div>
          </div>
        )}

        {/* Test Results */}
        {testResults.length > 0 && (
          <div>
            <h3 className="font-medium text-gray-900 mb-3">📋 Test Results</h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-md text-sm ${
                    result.status === 'success' || result.status === 200
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-red-50 border border-red-200'
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium">
                      {result.type === 'webhook' ? '🔗 Webhook' : '📷 Pixel'} Test
                    </span>
                    <span className="text-xs text-gray-500">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="text-gray-600">
                    Status: <span className="font-medium">{result.status}</span>
                    {result.statusText && ` (${result.statusText})`}
                    {result.error && ` - ${result.error}`}
                  </div>
                  {result.trackingId && (
                    <div className="text-xs text-gray-500 mt-1">
                      Tracking ID: {result.trackingId}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recent Tracking Data */}
        {recentTracking && recentTracking.length > 0 && (
          <div className="mt-6">
            <h3 className="font-medium text-gray-900 mb-3">📬 Recent Email Tracking</h3>
            <div className="space-y-2">
              {recentTracking.map((tracking) => (
                <div key={tracking._id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div>
                    <div className="text-sm font-medium">
                      {tracking.invoiceData ? 
                        `Invoice ${tracking.invoiceData.invoiceNumber}` : 
                        tracking.emailType
                      }
                    </div>
                    <div className="text-xs text-gray-500">{tracking.recipientEmail}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm">
                      {tracking.openCount} open{tracking.openCount !== 1 ? 's' : ''}
                    </div>
                    <div className="text-xs text-gray-500">
                      {tracking.lastOpenedAt ? 
                        new Date(tracking.lastOpenedAt).toLocaleString() : 
                        'Not opened'
                      }
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
