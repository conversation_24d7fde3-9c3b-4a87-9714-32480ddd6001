// PDF Email template utilities
// Specialized templates for PDF invoice delivery

import { formatCurrency, formatDate } from "./templates";

export interface PDFEmailOptions {
  includeDownloadLink: boolean;
  downloadUrl?: string;
  hasAttachment: boolean;
}

// Generate PDF-specific email template with download links and attachment info
export function generatePDFEmailTemplate(invoice: any, options: PDFEmailOptions, trackingPixelUrl?: string): string {
  // Generate itemized invoice details (condensed for PDF email)
  const itemsHtml = invoice.items?.slice(0, 5).map((item: any) => `
    <tr style="border-bottom: 1px solid #e5e7eb;">
      <td style="padding: 8px; text-align: left; font-size: 13px;">${item.description}</td>
      <td style="padding: 8px; text-align: center; font-size: 13px;">${item.quantity}</td>
      <td style="padding: 8px; text-align: right; font-size: 13px; font-weight: 600;">${formatCurrency(item.quantity * item.unitPrice)}</td>
    </tr>
  `).join('') || '';

  const hasMoreItems = invoice.items && invoice.items.length > 5;

  // Build customer information
  const customerInfo = [];
  if (invoice.customer?.name) customerInfo.push(invoice.customer.name);
  if (invoice.customer?.company) customerInfo.push(invoice.customer.company);

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <title>PDF Invoice ${invoice.invoiceNumber} from ${invoice.company.name}</title>
      <style>
        @media only screen and (max-width: 600px) {
          .container { width: 100% !important; padding: 10px !important; }
          .content { padding: 20px !important; }
          .header-title { font-size: 24px !important; }
          .invoice-number { font-size: 20px !important; }
          .button { padding: 12px 24px !important; font-size: 14px !important; }
          .items-table { font-size: 12px !important; }
          .items-table td { padding: 6px 4px !important; }
          .hide-mobile { display: none !important; }
        }
      </style>
    </head>
    <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333333; background-color: #f8fafc;">
      <div class="container" style="max-width: 600px; margin: 0 auto; padding: 20px;">
        
        <!-- Header with PDF Focus -->
        <div style="background: linear-gradient(135deg, #059669 0%, #**********%); padding: 30px; border-radius: 12px 12px 0 0; text-align: center; color: white;">
          ${invoice.company.logoUrl ? `
            <img src="${invoice.company.logoUrl}" alt="${invoice.company.name}" style="max-height: 50px; margin-bottom: 15px; display: block; margin-left: auto; margin-right: auto;">
          ` : ''}
          <h1 class="header-title" style="margin: 0; font-size: 26px; font-weight: 700;">📄 PDF Invoice Ready</h1>
          <p style="margin: 8px 0 0 0; font-size: 16px; opacity: 0.9;">Your invoice is ready for download</p>
        </div>

        <!-- Main Content -->
        <div class="content" style="background: white; padding: 30px; border-radius: 0 0 12px 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
          
          <!-- PDF Download Section -->
          ${options.includeDownloadLink && options.downloadUrl ? `
          <div style="background: #f0f9ff; padding: 25px; border-radius: 12px; margin-bottom: 25px; text-align: center; border: 2px solid #0ea5e9;">
            <h2 style="color: #0c4a6e; margin: 0 0 15px 0; font-size: 22px; font-weight: 600;">📥 Download Your Invoice</h2>
            <p style="color: #374151; margin: 0 0 20px 0; font-size: 16px;">Your PDF invoice is ready for download. Click the button below to access your invoice.</p>
            <a href="${options.downloadUrl}" style="display: inline-block; background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 6px rgba(14, 165, 233, 0.25);">
              📄 Download PDF Invoice
            </a>
            <p style="margin: 15px 0 0 0; color: #6b7280; font-size: 14px;">
              The download link will open your invoice in a new tab where you can view, print, or save it.
            </p>
          </div>
          ` : ''}

          <!-- Attachment Notice -->
          ${options.hasAttachment ? `
          <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 25px; text-align: center; border-left: 4px solid #f59e0b;">
            <p style="margin: 0; color: #92400e; font-size: 16px; font-weight: 600;">📎 PDF Attached</p>
            <p style="margin: 8px 0 0 0; color: #374151; font-size: 14px;">Your invoice PDF is also attached to this email for your convenience.</p>
          </div>
          ` : ''}

          <!-- Invoice Summary -->
          <div style="text-align: center; margin-bottom: 25px; padding-bottom: 20px; border-bottom: 2px solid #e5e7eb;">
            <h2 class="invoice-number" style="color: #1f2937; margin: 0 0 10px 0; font-size: 24px; font-weight: 600;">Invoice ${invoice.invoiceNumber}</h2>
            <p style="color: #6b7280; margin: 0; font-size: 16px;">From ${invoice.company.name}</p>
          </div>

          <!-- Customer Information -->
          <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #059669;">
            <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">Bill To:</h3>
            <div style="color: #374151;">
              ${customerInfo.map(info => `<p style="margin: 4px 0; font-weight: ${info === invoice.customer?.name ? '600' : '400'}; font-size: ${info === invoice.customer?.name ? '16px' : '14px'};">${info}</p>`).join('')}
              ${invoice.customer?.email ? `<p style="margin: 8px 0 4px 0; color: #059669; font-size: 14px;">📧 ${invoice.customer.email}</p>` : ''}
            </div>
          </div>

          <!-- Invoice Details -->
          <div style="display: flex; justify-content: space-between; margin-bottom: 25px; flex-wrap: wrap;">
            <div style="flex: 1; margin-right: 20px; min-width: 200px;">
              <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
                <p style="margin: 0 0 5px 0; color: #92400e; font-size: 12px; font-weight: 600; text-transform: uppercase;">Issue Date</p>
                <p style="margin: 0; color: #1f2937; font-size: 16px; font-weight: 600;">${formatDate(invoice.issueDate)}</p>
              </div>
            </div>
            <div style="flex: 1; min-width: 200px; margin-top: 10px;">
              <div style="background: #fecaca; padding: 15px; border-radius: 8px; border-left: 4px solid #ef4444;">
                <p style="margin: 0 0 5px 0; color: #991b1b; font-size: 12px; font-weight: 600; text-transform: uppercase;">Due Date</p>
                <p style="margin: 0; color: #1f2937; font-size: 16px; font-weight: 600;">${formatDate(invoice.dueDate)}</p>
              </div>
            </div>
          </div>

          <!-- Invoice Items Summary -->
          ${invoice.items && invoice.items.length > 0 ? `
          <div style="margin-bottom: 25px;">
            <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">Invoice Items:</h3>
            <div style="overflow-x: auto; border-radius: 8px; border: 1px solid #e5e7eb;">
              <table class="items-table" style="width: 100%; border-collapse: collapse; background: white;">
                <thead>
                  <tr style="background: #f9fafb;">
                    <th style="padding: 12px 8px; text-align: left; font-weight: 600; color: #374151; font-size: 14px; border-bottom: 2px solid #e5e7eb;">Description</th>
                    <th style="padding: 12px 8px; text-align: center; font-weight: 600; color: #374151; font-size: 14px; border-bottom: 2px solid #e5e7eb;">Qty</th>
                    <th style="padding: 12px 8px; text-align: right; font-weight: 600; color: #374151; font-size: 14px; border-bottom: 2px solid #e5e7eb;">Total</th>
                  </tr>
                </thead>
                <tbody>
                  ${itemsHtml}
                  ${hasMoreItems ? `
                  <tr>
                    <td colspan="3" style="padding: 12px 8px; text-align: center; font-style: italic; color: #6b7280; font-size: 13px; border-bottom: 1px solid #e5e7eb;">
                      ... and ${invoice.items.length - 5} more items (see PDF for complete details)
                    </td>
                  </tr>
                  ` : ''}
                </tbody>
              </table>
            </div>
          </div>
          ` : ''}

          <!-- Invoice Total -->
          <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin-bottom: 25px; border: 2px solid #059669;">
            <div style="text-align: center;">
              <p style="margin: 0 0 5px 0; color: #047857; font-size: 14px; font-weight: 600; text-transform: uppercase;">Total Amount</p>
              <p style="margin: 0; color: #1f2937; font-size: 32px; font-weight: 700;">${formatCurrency(invoice.total)}</p>
              ${invoice.taxAmount > 0 ? `<p style="margin: 5px 0 0 0; color: #6b7280; font-size: 12px;">Includes ${formatCurrency(invoice.taxAmount)} in taxes</p>` : ''}
            </div>
          </div>

          <!-- Call to Action (Repeat) -->
          ${options.includeDownloadLink && options.downloadUrl ? `
          <div style="text-align: center; margin-bottom: 25px;">
            <a href="${options.downloadUrl}" style="display: inline-block; background: linear-gradient(135deg, #059669 0%, #**********%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 6px rgba(5, 150, 105, 0.25);">
              📄 Download PDF Invoice
            </a>
          </div>
          ` : ''}

          <!-- Payment Terms -->
          ${invoice.company.paymentTerms ? `
          <div style="background: #f3f4f6; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
            <p style="margin: 0; color: #374151; font-size: 14px;"><strong>Payment Terms:</strong> ${invoice.company.paymentTerms}</p>
          </div>
          ` : ''}

          <!-- Notes Section -->
          ${invoice.notes ? `
          <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #10b981;">
            <h4 style="margin: 0 0 8px 0; color: #065f46; font-size: 14px; font-weight: 600;">Additional Notes:</h4>
            <p style="margin: 0; color: #374151; font-size: 14px; white-space: pre-line;">${invoice.notes}</p>
          </div>
          ` : ''}
        </div>

        <!-- Footer -->
        <div style="background: #1f2937; padding: 25px; border-radius: 12px; margin-top: 20px; text-align: center; color: white;">
          <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">${invoice.company.name}</h3>
          <div style="color: #d1d5db; font-size: 14px; line-height: 1.5;">
            ${invoice.company.address ? `<p style="margin: 3px 0;">${invoice.company.address}</p>` : ''}
            ${invoice.company.city && invoice.company.state ? `<p style="margin: 3px 0;">${invoice.company.city}, ${invoice.company.state} ${invoice.company.zipCode || ''}</p>` : ''}
            <div style="margin: 10px 0;">
              ${invoice.company.contactPhone ? `<span style="margin: 0 10px;">📞 ${invoice.company.contactPhone}</span>` : ''}
              ${invoice.company.contactEmail ? `<span style="margin: 0 10px;">📧 ${invoice.company.contactEmail}</span>` : ''}
            </div>
          </div>
          
          <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #374151;">
            <p style="margin: 0; font-size: 16px; font-weight: 600;">Thank you for your business! 🙏</p>
          </div>
        </div>

        <!-- Email Footer -->
        <div style="text-align: center; margin-top: 20px; color: #6b7280; font-size: 12px;">
          <p style="margin: 0;">This is an automated email. Please do not reply to this message.</p>
          <p style="margin: 5px 0 0 0;">If you have questions, please contact us at ${invoice.company.contactEmail || 'your support email'}.</p>
        </div>

        ${trackingPixelUrl ? `<!-- Email tracking pixel -->
        <img src="${trackingPixelUrl}" alt="" style="width: 1px; height: 1px; border: 0; display: block;" />` : ''}
      </div>
    </body>
    </html>
  `;
}
