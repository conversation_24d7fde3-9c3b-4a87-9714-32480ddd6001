// Email template utilities and generators
// Separated from main email service for better organization

// Utility functions for formatting
export const formatCurrency = (amount: number): string => 
  `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;

export const formatDate = (timestamp: number): string => 
  new Date(timestamp).toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });

// Generate enhanced notification email HTML with comprehensive customer information
export function generateNotificationEmail(invoice: any, invoiceUrl: string, trackingPixelUrl?: string): string {
  // Generate itemized invoice details
  const itemsHtml = invoice.items?.map((item: any) => `
    <tr style="border-bottom: 1px solid #e5e7eb;">
      <td style="padding: 12px 8px; text-align: left; font-size: 14px;">${item.description}</td>
      <td style="padding: 12px 8px; text-align: center; font-size: 14px;">${item.quantity}</td>
      <td style="padding: 12px 8px; text-align: right; font-size: 14px;">${formatCurrency(item.unitPrice)}</td>
      <td style="padding: 12px 8px; text-align: right; font-size: 14px; font-weight: 600;">${formatCurrency(item.quantity * item.unitPrice)}</td>
    </tr>
  `).join('') || '';

  // Build comprehensive customer information
  const customerInfo = [];
  if (invoice.customer?.name) customerInfo.push(invoice.customer.name);
  if (invoice.customer?.company) customerInfo.push(invoice.customer.company);
  if (invoice.customer?.industry) customerInfo.push(`Industry: ${invoice.customer.industry}`);
  
  const customerAddress = [];
  if (invoice.customer?.address) customerAddress.push(invoice.customer.address);
  if (invoice.customer?.city && invoice.customer?.state) {
    customerAddress.push(`${invoice.customer.city}, ${invoice.customer.state} ${invoice.customer?.zipCode || ''}`);
  }
  if (invoice.customer?.county) customerAddress.push(`${invoice.customer.county} County`);

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <title>Invoice ${invoice.invoiceNumber} from ${invoice.company.name}</title>
      <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <![endif]-->
      <style>
        @media only screen and (max-width: 600px) {
          .container { width: 100% !important; padding: 10px !important; }
          .content { padding: 20px !important; }
          .header-title { font-size: 24px !important; }
          .invoice-number { font-size: 20px !important; }
          .button { padding: 12px 24px !important; font-size: 14px !important; }
          .items-table { font-size: 12px !important; }
          .items-table td { padding: 8px 4px !important; }
          .hide-mobile { display: none !important; }
          .mobile-stack { display: block !important; width: 100% !important; }
        }
        @media only screen and (max-width: 480px) {
          .items-table td:nth-child(2), .items-table th:nth-child(2) { display: none !important; }
          .items-table td:nth-child(3), .items-table th:nth-child(3) { display: none !important; }
        }
      </style>
    </head>
    <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333333; background-color: #f8fafc;">
      <div class="container" style="max-width: 600px; margin: 0 auto; padding: 20px;">
        
        <!-- Header with Company Branding -->
        <div style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); padding: 30px; border-radius: 12px 12px 0 0; text-align: center; color: white;">
          ${invoice.company.logoUrl ? `
            <img src="${invoice.company.logoUrl}" alt="${invoice.company.name}" style="max-height: 60px; margin-bottom: 15px; display: block; margin-left: auto; margin-right: auto;">
          ` : ''}
          <h1 class="header-title" style="margin: 0; font-size: 28px; font-weight: 700; letter-spacing: -0.5px;">${invoice.company.name}</h1>
          <p style="margin: 8px 0 0 0; font-size: 16px; opacity: 0.9;">Professional Invoice</p>
        </div>

        <!-- Main Content -->
        <div class="content" style="background: white; padding: 30px; border-radius: 0 0 12px 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
          
          <!-- Invoice Header -->
          <div style="text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e5e7eb;">
            <h2 class="invoice-number" style="color: #1f2937; margin: 0 0 10px 0; font-size: 24px; font-weight: 600;">Invoice ${invoice.invoiceNumber}</h2>
            <p style="color: #6b7280; margin: 0; font-size: 16px;">New invoice ready for your review</p>
          </div>

          <!-- Customer Information Section -->
          <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #2563eb;">
            <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">Bill To:</h3>
            <div style="color: #374151;">
              ${customerInfo.map(info => `<p style="margin: 4px 0; font-weight: ${info === invoice.customer?.name ? '600' : '400'}; font-size: ${info === invoice.customer?.name ? '16px' : '14px'};">${info}</p>`).join('')}
              ${invoice.customer?.email ? `<p style="margin: 8px 0 4px 0; color: #2563eb; font-size: 14px;">📧 ${invoice.customer.email}</p>` : ''}
              ${invoice.customer?.phone ? `<p style="margin: 4px 0; color: #059669; font-size: 14px;">📞 ${invoice.customer.phone}</p>` : ''}
              ${customerAddress.length > 0 ? `<div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #e5e7eb;">${customerAddress.map(addr => `<p style="margin: 2px 0; color: #6b7280; font-size: 14px;">📍 ${addr}</p>`).join('')}</div>` : ''}
            </div>
          </div>

          <!-- Invoice Details -->
          <div style="display: flex; justify-content: space-between; margin-bottom: 25px; flex-wrap: wrap;">
            <div class="mobile-stack" style="flex: 1; margin-right: 20px; min-width: 200px;">
              <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b;">
                <p style="margin: 0 0 5px 0; color: #92400e; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">Issue Date</p>
                <p style="margin: 0; color: #1f2937; font-size: 16px; font-weight: 600;">${formatDate(invoice.issueDate)}</p>
              </div>
            </div>
            <div class="mobile-stack" style="flex: 1; min-width: 200px; margin-top: 10px;">
              <div style="background: #fecaca; padding: 15px; border-radius: 8px; border-left: 4px solid #ef4444;">
                <p style="margin: 0 0 5px 0; color: #991b1b; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">Due Date</p>
                <p style="margin: 0; color: #1f2937; font-size: 16px; font-weight: 600;">${formatDate(invoice.dueDate)}</p>
              </div>
            </div>
          </div>

          <!-- Invoice Items (if available) -->
          ${invoice.items && invoice.items.length > 0 ? `
          <div style="margin-bottom: 25px;">
            <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">Invoice Items:</h3>
            <div style="overflow-x: auto; border-radius: 8px; border: 1px solid #e5e7eb;">
              <table class="items-table" style="width: 100%; border-collapse: collapse; background: white;">
                <thead>
                  <tr style="background: #f9fafb;">
                    <th style="padding: 12px 8px; text-align: left; font-weight: 600; color: #374151; font-size: 14px; border-bottom: 2px solid #e5e7eb;">Description</th>
                    <th style="padding: 12px 8px; text-align: center; font-weight: 600; color: #374151; font-size: 14px; border-bottom: 2px solid #e5e7eb;">Qty</th>
                    <th style="padding: 12px 8px; text-align: right; font-weight: 600; color: #374151; font-size: 14px; border-bottom: 2px solid #e5e7eb;">Unit Price</th>
                    <th style="padding: 12px 8px; text-align: right; font-weight: 600; color: #374151; font-size: 14px; border-bottom: 2px solid #e5e7eb;">Total</th>
                  </tr>
                </thead>
                <tbody>
                  ${itemsHtml}
                </tbody>
              </table>
            </div>
          </div>
          ` : ''}

          <!-- Invoice Total -->
          <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin-bottom: 25px; border: 2px solid #2563eb;">
            <div style="text-align: center;">
              <p style="margin: 0 0 5px 0; color: #1e40af; font-size: 14px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">Total Amount</p>
              <p style="margin: 0; color: #1f2937; font-size: 32px; font-weight: 700;">${formatCurrency(invoice.total)}</p>
              ${invoice.taxAmount > 0 ? `<p style="margin: 5px 0 0 0; color: #6b7280; font-size: 12px;">Includes ${formatCurrency(invoice.taxAmount)} in taxes</p>` : ''}
            </div>
          </div>

          <!-- Call to Action -->
          <div style="text-align: center; margin-bottom: 25px;">
            <a href="${invoiceUrl}" class="button" style="display: inline-block; background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25); transition: all 0.2s;">
              📄 View & Download Invoice
            </a>
            <p style="margin: 15px 0 0 0; color: #6b7280; font-size: 14px;">
              Click the button above to view your complete invoice and download a PDF copy.
            </p>
          </div>

          <!-- Payment Terms -->
          ${invoice.company.paymentTerms ? `
          <div style="background: #f3f4f6; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
            <p style="margin: 0; color: #374151; font-size: 14px;"><strong>Payment Terms:</strong> ${invoice.company.paymentTerms}</p>
          </div>
          ` : ''}

          <!-- Notes Section -->
          ${invoice.notes ? `
          <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #10b981;">
            <h4 style="margin: 0 0 8px 0; color: #065f46; font-size: 14px; font-weight: 600;">Additional Notes:</h4>
            <p style="margin: 0; color: #374151; font-size: 14px; white-space: pre-line;">${invoice.notes}</p>
          </div>
          ` : ''}
        </div>

        <!-- Footer -->
        <div style="background: #1f2937; padding: 25px; border-radius: 12px; margin-top: 20px; text-align: center; color: white;">
          <h3 style="margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">${invoice.company.name}</h3>
          <div style="color: #d1d5db; font-size: 14px; line-height: 1.5;">
            ${invoice.company.address ? `<p style="margin: 3px 0;">${invoice.company.address}</p>` : ''}
            ${invoice.company.city && invoice.company.state ? `<p style="margin: 3px 0;">${invoice.company.city}, ${invoice.company.state} ${invoice.company.zipCode || ''}</p>` : ''}
            <div style="margin: 10px 0;">
              ${invoice.company.contactPhone ? `<span style="margin: 0 10px;">📞 ${invoice.company.contactPhone}</span>` : ''}
              ${invoice.company.contactEmail ? `<span style="margin: 0 10px;">📧 ${invoice.company.contactEmail}</span>` : ''}
            </div>
            ${invoice.company.taxId ? `<p style="margin: 10px 0 0 0; font-size: 12px; opacity: 0.8;">Tax ID: ${invoice.company.taxId}</p>` : ''}
          </div>
          
          <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #374151;">
            <p style="margin: 0; font-size: 16px; font-weight: 600;">Thank you for your business! 🙏</p>
            ${invoice.footer ? `<p style="margin: 8px 0 0 0; font-size: 12px; opacity: 0.8;">${invoice.footer}</p>` : ''}
          </div>
        </div>

        <!-- Email Footer -->
        <div style="text-align: center; margin-top: 20px; color: #6b7280; font-size: 12px;">
          <p style="margin: 0;">This is an automated email. Please do not reply to this message.</p>
          <p style="margin: 5px 0 0 0;">If you have questions, please contact us at ${invoice.company.contactEmail || 'your support email'}.</p>
        </div>

        ${trackingPixelUrl ? `<!-- Email tracking pixel -->
        <img src="${trackingPixelUrl}" alt="" style="width: 1px; height: 1px; border: 0; display: block;" />` : ''}
      </div>
    </body>
    </html>
  `;
}

// Generate invoice HTML for storage and viewing
export function generateInvoiceHTML(invoice: any): string {
  const itemsHtml = invoice.items?.map((item: any) => `
    <tr>
      <td style="padding: 12px; border-bottom: 1px solid #e5e7eb;">${item.description}</td>
      <td style="padding: 12px; border-bottom: 1px solid #e5e7eb; text-align: center;">${item.quantity}</td>
      <td style="padding: 12px; border-bottom: 1px solid #e5e7eb; text-align: right;">${formatCurrency(item.unitPrice)}</td>
      <td style="padding: 12px; border-bottom: 1px solid #e5e7eb; text-align: right;">${formatCurrency(item.quantity * item.unitPrice)}</td>
    </tr>
  `).join('') || '';

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice ${invoice.invoiceNumber}</title>
      <style>
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
        .print-button {
          position: fixed;
          top: 20px;
          right: 20px;
          background: #2563eb;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 6px;
          cursor: pointer;
          font-weight: bold;
          z-index: 1000;
        }
        .print-button:hover {
          background: #1d4ed8;
        }
      </style>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px;">
      <button class="print-button no-print" onclick="window.print()">🖨️ Print Invoice</button>

      <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <!-- Header -->
        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 40px;">
          <div>
            <h1 style="color: #2563eb; margin: 0; font-size: 28px;">${invoice.company.name}</h1>
            <div style="margin-top: 10px; color: #6b7280;">
              ${invoice.company.address ? `<p style="margin: 2px 0;">${invoice.company.address}</p>` : ''}
              ${invoice.company.city && invoice.company.state ? `<p style="margin: 2px 0;">${invoice.company.city}, ${invoice.company.state} ${invoice.company.zipCode || ''}</p>` : ''}
              ${invoice.company.contactPhone ? `<p style="margin: 2px 0;">Phone: ${invoice.company.contactPhone}</p>` : ''}
              ${invoice.company.contactEmail ? `<p style="margin: 2px 0;">Email: ${invoice.company.contactEmail}</p>` : ''}
            </div>
          </div>
          <div style="text-align: right;">
            <h2 style="color: #1f2937; margin: 0; font-size: 24px;">INVOICE</h2>
            <p style="margin: 5px 0; font-size: 18px; font-weight: bold;">${invoice.invoiceNumber}</p>
            <p style="margin: 2px 0; color: #6b7280;">Date: ${formatDate(invoice.issueDate)}</p>
            <p style="margin: 2px 0; color: #6b7280;">Due: ${formatDate(invoice.dueDate)}</p>
          </div>
        </div>

        <!-- Bill To -->
        <div style="margin-bottom: 30px;">
          <h3 style="color: #1f2937; margin-bottom: 10px;">Bill To:</h3>
          <div style="background: white; padding: 20px; border-radius: 6px; border-left: 4px solid #2563eb;">
            <p style="margin: 0; font-weight: bold; font-size: 16px;">${invoice.customer?.name || 'Unknown Customer'}</p>
            ${invoice.customer?.email ? `<p style="margin: 5px 0; color: #6b7280;">${invoice.customer.email}</p>` : ''}
            ${invoice.customer?.phone ? `<p style="margin: 5px 0; color: #6b7280;">${invoice.customer.phone}</p>` : ''}
            ${invoice.customer?.address ? `<p style="margin: 5px 0; color: #6b7280;">${invoice.customer.address}</p>` : ''}
            ${invoice.customer?.city && invoice.customer?.state ? `<p style="margin: 5px 0; color: #6b7280;">${invoice.customer.city}, ${invoice.customer.state} ${invoice.customer?.zipCode || ''}</p>` : ''}
          </div>
        </div>

        <!-- Items Table -->
        <div style="margin-bottom: 30px;">
          <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 6px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <thead>
              <tr style="background: #f3f4f6;">
                <th style="padding: 15px; text-align: left; font-weight: 600; color: #374151;">Description</th>
                <th style="padding: 15px; text-align: center; font-weight: 600; color: #374151;">Qty</th>
                <th style="padding: 15px; text-align: right; font-weight: 600; color: #374151;">Unit Price</th>
                <th style="padding: 15px; text-align: right; font-weight: 600; color: #374151;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
            </tbody>
          </table>
        </div>

        <!-- Totals -->
        <div style="display: flex; justify-content: flex-end; margin-bottom: 30px;">
          <div style="background: white; padding: 25px; border-radius: 6px; min-width: 300px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px; padding-bottom: 10px;">
              <span style="color: #6b7280;">Subtotal:</span>
              <span style="font-weight: 500;">${formatCurrency(invoice.subtotal)}</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 15px; padding-bottom: 15px;">
              <span style="color: #6b7280;">Tax (${invoice.taxRate}%):</span>
              <span style="font-weight: 500;">${formatCurrency(invoice.taxAmount)}</span>
            </div>
            <div style="display: flex; justify-content: space-between; border-top: 2px solid #e5e7eb; padding-top: 15px;">
              <span style="font-size: 18px; font-weight: 600; color: #1f2937;">Total:</span>
              <span style="font-size: 20px; font-weight: 700; color: #2563eb;">${formatCurrency(invoice.total)}</span>
            </div>
          </div>
        </div>

        <!-- Notes -->
        ${invoice.notes ? `
        <div style="margin-bottom: 30px;">
          <h3 style="color: #1f2937; margin-bottom: 10px;">Notes:</h3>
          <div style="background: white; padding: 20px; border-radius: 6px; border-left: 4px solid #10b981;">
            <p style="margin: 0; color: #374151; white-space: pre-line;">${invoice.notes}</p>
          </div>
        </div>
        ` : ''}

        <!-- Footer -->
        <div style="text-align: center; padding-top: 30px; border-top: 1px solid #e5e7eb; color: #6b7280;">
          <p style="margin: 0;">Thank you for your business!</p>
          ${invoice.footer ? `<p style="margin: 10px 0 0 0; font-size: 14px;">${invoice.footer}</p>` : ''}
          ${invoice.company.taxId ? `<p style="margin: 10px 0 0 0; font-size: 12px;">Tax ID: ${invoice.company.taxId}</p>` : ''}
        </div>
      </div>
    </body>
    </html>
  `;
}
