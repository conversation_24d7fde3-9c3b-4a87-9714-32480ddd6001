import { v } from "convex/values";
import { action } from "./_generated/server";
import { internal, api } from "./_generated/api";
import { generatePDFEmailTemplate } from "./email/pdfTemplates";
import {
  validateInvoiceForEmail,
  validateCompanySettings,
  validateResendConfig
} from "./email/validation";
import {
  createResendClient,
  getEmailConfig,
  sendEmailWithAttachment,
  generateEmailSubject,
  logEmailOperation,
  validateEmailContent
} from "./email/pdfService";
import {
  generateTemplateVariables,
  replaceTemplateVariables
} from "./email/service";
import { generateTrackingPixelUrl } from "./emailTracking";
import { getAuthUserId } from "@convex-dev/auth/server";

// Generate PDF and send via email with attachment and/or download link
export const generateAndSendPDFEmail = action({
  args: {
    invoiceId: v.id("invoices"),
    options: v.optional(v.object({
      generatePDF: v.optional(v.boolean()),
      includeAttachment: v.optional(v.boolean()),
      includeDownloadLink: v.optional(v.boolean()),
      cleanupOldPDF: v.optional(v.boolean()),
    }))
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    message: string;
    emailId?: string;
    pdfStorageId?: string;
    customerEmail?: string;
    invoiceNumber?: string;
    downloadUrl?: string;
  }> => {
    try {
      // Set default options
      const options = {
        generatePDF: true,
        includeAttachment: false, // Don't attach by default to avoid large emails
        includeDownloadLink: true,
        cleanupOldPDF: false,
        ...args.options
      };

      logEmailOperation("pdf-email-start", args.invoiceId, { options });

      // Get invoice with customer data
      const invoice: any = await ctx.runQuery(internal.invoices.getInvoiceWithCustomer, {
        invoiceId: args.invoiceId
      });

      if (!invoice) {
        throw new Error("Invoice not found or you don't have permission to access it");
      }

      // Validate invoice for email sending
      const invoiceValidation = validateInvoiceForEmail(invoice, args.invoiceId);
      if (!invoiceValidation.isValid) {
        throw new Error(invoiceValidation.error);
      }

      // Get company settings
      const settings = await ctx.runQuery(internal.settings.getForInvoice, {
        userId: invoice.createdBy
      });

      if (!settings) {
        throw new Error("Company settings not found. Please configure company settings first.");
      }

      // Validate company settings
      const settingsValidation = validateCompanySettings(settings);
      if (!settingsValidation.isValid) {
        throw new Error(settingsValidation.error);
      }

      // Validate Resend configuration
      const resendValidation = validateResendConfig();
      if (!resendValidation.isValid) {
        throw new Error(resendValidation.error);
      }

      // Validate from email (removed as it doesn't exist in validation.ts)
      // Email validation is handled by validateResendConfig

      // Create branded invoice object for email template
      const brandedInvoice = {
        ...invoice,
        company: settings
      };

      let pdfStorageId = invoice.pdfStorageId;
      let pdfUrl: string | undefined;

      // Generate PDF if requested or if no PDF exists
      if (options.generatePDF || !pdfStorageId) {
        logEmailOperation("pdf-generation-start", args.invoiceId, {});

        // Note: PDF generation happens on client side, so we need to handle this differently
        // For now, we'll check if PDF exists and provide instructions if not
        if (!pdfStorageId) {
          throw new Error(
            "No PDF available for this invoice. Please generate a PDF first using the 'Generate PDF' button, " +
            "then try sending the email again."
          );
        }
      }

      // Get PDF URL for download link
      if (options.includeDownloadLink && pdfStorageId) {
        try {
          logEmailOperation("pdf-url-generation-start", args.invoiceId, {
            pdfStorageId: pdfStorageId.substring(0, 20) + "...",
            includeDownloadLink: options.includeDownloadLink
          });

          const url = await ctx.storage.getUrl(pdfStorageId);
          pdfUrl = url || undefined;

          if (!pdfUrl) {
            logEmailOperation("pdf-url-error", args.invoiceId, {
              error: "Failed to get PDF URL from storage - URL is null",
              pdfStorageId: pdfStorageId.substring(0, 20) + "..."
            });
          } else {
            // Validate URL format
            if (!pdfUrl.startsWith('http')) {
              logEmailOperation("pdf-url-error", args.invoiceId, {
                error: "Invalid PDF URL format - does not start with http",
                urlPrefix: pdfUrl.substring(0, 50) + "..."
              });
              pdfUrl = undefined;
            } else {
              logEmailOperation("pdf-url-success", args.invoiceId, {
                urlPrefix: pdfUrl.substring(0, 50) + "...",
                urlLength: pdfUrl.length
              });
            }
          }
        } catch (error) {
          logEmailOperation("pdf-url-error", args.invoiceId, {
            error: error instanceof Error ? error.message : "Unknown error getting PDF URL",
            pdfStorageId: pdfStorageId.substring(0, 20) + "..."
          });
        }
      } else {
        logEmailOperation("pdf-url-skipped", args.invoiceId, {
          includeDownloadLink: options.includeDownloadLink,
          hasPdfStorageId: !!pdfStorageId,
          reason: !options.includeDownloadLink ? "includeDownloadLink is false" : "no pdfStorageId"
        });
      }

      // Generate tracking pixel URL for email tracking
      const trackingPixelUrl = generateTrackingPixelUrl(`pdf_${args.invoiceId}_${Date.now()}`);
      const baseUrl = process.env.CONVEX_SITE_URL || process.env.CONVEX_CLOUD_URL || 'https://your-site.convex.site';
      const fullTrackingPixelUrl = `${baseUrl}${trackingPixelUrl}`;

      // Get email configuration and create Resend client
      const emailConfig = getEmailConfig(settings);
      const resend = createResendClient(emailConfig.apiKey);

      // Generate email subject and content using database templates
      let emailSubject: string;
      let emailHtml: string;

      try {
        // Try to get custom email template from database
        const emailTemplate = await ctx.runQuery(internal.emailTemplates.getByCategoryInternal, {
          category: "invoice"
        });

        if (emailTemplate) {
          // Use custom template with PDF-specific variables
          // Use PDF download URL as the main invoice URL for the "View & Download Invoice" button
          const templateVariables = generateTemplateVariables(brandedInvoice, settings, pdfUrl, {
            downloadUrl: pdfUrl,
            hasAttachment: options.includeAttachment,
            downloadInstructions: pdfUrl ?
              "Click the download button below to get your PDF invoice. The download link will remain active for 7 days." :
              "Your invoice PDF is attached to this email."
          });

          emailSubject = replaceTemplateVariables(emailTemplate.subject, templateVariables);
          emailHtml = replaceTemplateVariables(emailTemplate.content, templateVariables);

          // Validate that the email HTML contains proper download links
          const hasValidInvoiceUrl = emailHtml.includes('href="http') && !emailHtml.includes('href=""');
          if (!hasValidInvoiceUrl && pdfUrl) {
            logEmailOperation("template-validation-warning", args.invoiceId, {
              warning: "Email template may not contain valid download links",
              hasPdfUrl: !!pdfUrl,
              templateContainsHref: emailHtml.includes('href='),
              templateContainsEmptyHref: emailHtml.includes('href=""')
            });
          }

          logEmailOperation("custom-template-used-pdf", args.invoiceId, {
            templateId: emailTemplate._id,
            templateName: emailTemplate.name,
            hasDownloadUrl: !!pdfUrl,
            hasAttachment: options.includeAttachment,
            invoiceUrlLength: templateVariables.invoiceUrl?.length || 0,
            pdfDownloadUrlLength: templateVariables.pdfDownloadUrl?.length || 0,
            hasPdfDownloadButton: templateVariables.pdfDownloadButton?.length > 0,
            downloadInstructionsLength: templateVariables.downloadInstructions?.length || 0
          });
        } else {
          // Fallback to hardcoded PDF template with tracking pixel
          emailSubject = `Invoice ${invoice.invoiceNumber} - PDF Ready for Download`;
          emailHtml = generatePDFEmailTemplate(brandedInvoice, {
            includeDownloadLink: options.includeDownloadLink,
            downloadUrl: pdfUrl,
            hasAttachment: options.includeAttachment
          }, fullTrackingPixelUrl);

          logEmailOperation("fallback-template-used-pdf", args.invoiceId, {
            reason: "no-custom-template-found",
            hasDownloadUrl: !!pdfUrl,
            hasAttachment: options.includeAttachment
          });
        }
      } catch (error) {
        // Fallback to hardcoded template on any error
        emailSubject = `Invoice ${invoice.invoiceNumber} - PDF Ready for Download`;
        emailHtml = generatePDFEmailTemplate(brandedInvoice, {
          includeDownloadLink: options.includeDownloadLink,
          downloadUrl: pdfUrl,
          hasAttachment: options.includeAttachment
        });

        logEmailOperation("fallback-template-used-pdf", args.invoiceId, {
          reason: "template-retrieval-error",
          error: error instanceof Error ? error.message : "Unknown error",
          hasDownloadUrl: !!pdfUrl,
          hasAttachment: options.includeAttachment
        });
      }

      // Validate email content
      const contentValidation = validateEmailContent(emailSubject, emailHtml);
      if (!contentValidation.isValid) {
        throw new Error(contentValidation.error);
      }

      // Prepare attachment if requested
      let attachment: any = undefined;
      if (options.includeAttachment && pdfStorageId) {
        try {
          const pdfBlob = await ctx.storage.get(pdfStorageId);
          if (pdfBlob) {
            const arrayBuffer = await pdfBlob.arrayBuffer();
            attachment = {
              filename: `Invoice-${invoice.invoiceNumber}.pdf`,
              content: Buffer.from(arrayBuffer),
              type: 'application/pdf'
            };
          }
        } catch (error) {
          logEmailOperation("pdf-attachment-error", args.invoiceId, { 
            error: error instanceof Error ? error.message : "Unknown error preparing attachment" 
          });
          // Continue without attachment if there's an error
        }
      }

      // Send email
      const emailResult = await sendEmailWithAttachment(
        resend,
        emailConfig,
        invoice.customer.email,
        emailSubject,
        emailHtml,
        attachment
      );

      if (!emailResult.success) {
        throw new Error(emailResult.error);
      }

      logEmailOperation("pdf-email-sent", args.invoiceId, {
        emailId: emailResult.emailId,
        customerEmail: invoice.customer.email,
        subject: emailSubject,
        hasAttachment: !!attachment,
        hasDownloadLink: !!pdfUrl
      });

      // Create email tracking record for PDF email
      try {
        const userId = await getAuthUserId(ctx);
        await ctx.runMutation(internal.emailTracking.createEmailTracking, {
          emailId: emailResult.emailId!,
          invoiceId: args.invoiceId,
          recipientEmail: invoice.customer.email,
          emailType: 'invoice_pdf',
          trackingPixelUrl: fullTrackingPixelUrl,
          isTracked: true,
          metadata: {
            subject: emailSubject,
            templateType: 'pdf',
            invoiceNumber: invoice.invoiceNumber,
            hasAttachment: !!attachment,
            hasDownloadLink: !!pdfUrl
          },
          sentBy: userId!
        });
        logEmailOperation("pdf-tracking-created", args.invoiceId, {
          emailId: emailResult.emailId,
          trackingPixelUrl: fullTrackingPixelUrl
        });
      } catch (trackingError) {
        console.error("Error creating PDF email tracking:", trackingError);
        // Don't fail the operation if tracking creation fails
      }

      // Mark invoice as sent if it's in draft status
      try {
        await ctx.runMutation(internal.invoices.markEmailSent, {
          invoiceId: args.invoiceId,
        });
        logEmailOperation("invoice-marked-sent", args.invoiceId, {});
      } catch (markError) {
        console.error("Error marking invoice as sent:", markError);
        // Don't fail the operation if this update fails, email was sent successfully
      }

      const successMessage: string = `PDF invoice ${invoice.invoiceNumber} sent successfully to ${invoice.customer.email}`;
      logEmailOperation("pdf-email-complete", args.invoiceId, {
        success: true,
        emailId: emailResult.emailId,
        message: successMessage
      });

      return {
        success: true,
        message: successMessage,
        emailId: emailResult.emailId,
        pdfStorageId,
        customerEmail: invoice.customer.email,
        invoiceNumber: invoice.invoiceNumber,
        downloadUrl: pdfUrl,
      };

    } catch (error: any) {
      logEmailOperation("pdf-email-error", args.invoiceId, {
        error: error.message,
        stack: error.stack
      });

      // Re-throw with enhanced error message for user
      const userFriendlyMessage = error.message || "An unexpected error occurred while sending the PDF invoice email.";
      throw new Error(userFriendlyMessage);
    }
  }
});

// Generate PDF on client and send via email (combined workflow)
export const generatePDFAndSendEmail = action({
  args: {
    invoiceId: v.id("invoices"),
    pdfBlob: v.any(), // PDF blob data from client (ArrayBuffer)
    options: v.optional(v.object({
      includeAttachment: v.optional(v.boolean()),
      includeDownloadLink: v.optional(v.boolean()),
      cleanupOldPDF: v.optional(v.boolean()),
    }))
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    message: string;
    emailId?: string;
    pdfStorageId?: string;
    customerEmail?: string;
    invoiceNumber?: string;
    downloadUrl?: string;
  }> => {
    try {
      // Set default options
      const options = {
        includeAttachment: false,
        includeDownloadLink: true,
        cleanupOldPDF: true,
        ...args.options
      };

      logEmailOperation("pdf-generate-and-email-start", args.invoiceId, { options });

      // First, store the PDF (using public action since it's not internal)
      const storeResult = await ctx.runAction(api.invoices.storePDFBlob, {
        invoiceId: args.invoiceId,
        pdfBlob: args.pdfBlob
      });

      if (!storeResult.success) {
        throw new Error(storeResult.message || "Failed to store PDF");
      }

      // Then send the email (using public action since it's not internal)
      const emailResult: any = await ctx.runAction(api.invoicePDFEmail.generateAndSendPDFEmail, {
        invoiceId: args.invoiceId,
        options: {
          generatePDF: false, // PDF already generated
          ...options
        }
      });

      return emailResult;

    } catch (error: any) {
      logEmailOperation("pdf-generate-and-email-error", args.invoiceId, {
        error: error.message,
        stack: error.stack
      });

      throw new Error(error.message || "Failed to generate PDF and send email");
    }
  }
});
