import { v } from "convex/values";
import { query, mutation, internalMutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Get notifications for the current user
export const getNotifications = query({
  args: {
    limit: v.optional(v.number()),
    unreadOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    const limit = args.limit || 20;
    
    let query = ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc");
    
    if (args.unreadOnly) {
      query = query.filter((q) => q.eq(q.field("isRead"), false));
    }
    
    const notifications = await query.take(limit);
    
    // Also get broadcast notifications (userId is null)
    const broadcastQuery = ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", null))
      .order("desc");
    
    const broadcastNotifications = await broadcastQuery.take(limit);
    
    // Combine and sort by creation time
    const allNotifications = [...notifications, ...broadcastNotifications]
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(0, limit);
    
    return allNotifications;
  },
});

// Get unread notification count
export const getUnreadCount = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    
    const userNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("isRead"), false))
      .collect();
    
    const broadcastNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", null))
      .filter((q) => q.eq(q.field("isRead"), false))
      .collect();
    
    return userNotifications.length + broadcastNotifications.length;
  },
});

// Mark notification as read
export const markAsRead = mutation({
  args: { notificationId: v.id("notifications") },
  handler: async (ctx, args) => {
    const userId = await getCurrentUser(ctx);
    
    const notification = await ctx.db.get(args.notificationId);
    if (!notification) {
      throw new Error("Notification not found");
    }
    
    // Only allow marking own notifications or broadcast notifications as read
    if (notification.userId !== userId && notification.userId !== null) {
      throw new Error("Not authorized to mark this notification as read");
    }
    
    await ctx.db.patch(args.notificationId, {
      isRead: true,
      readAt: Date.now(),
    });
    
    return { success: true };
  },
});

// Mark all notifications as read
export const markAllAsRead = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    
    // Get all unread notifications for the user
    const userNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("isRead"), false))
      .collect();
    
    // Get all unread broadcast notifications
    const broadcastNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", null))
      .filter((q) => q.eq(q.field("isRead"), false))
      .collect();
    
    const allNotifications = [...userNotifications, ...broadcastNotifications];
    const now = Date.now();
    
    // Mark all as read
    await Promise.all(
      allNotifications.map(notification =>
        ctx.db.patch(notification._id, {
          isRead: true,
          readAt: now,
        })
      )
    );
    
    return { success: true, count: allNotifications.length };
  },
});

// Create a notification (internal)
export const createNotification = internalMutation({
  args: {
    type: v.string(),
    title: v.string(),
    message: v.string(),
    data: v.optional(v.any()),
    userId: v.optional(v.id("users")), // null for broadcast
  },
  handler: async (ctx, args) => {
    const notificationId = await ctx.db.insert("notifications", {
      type: args.type,
      title: args.title,
      message: args.message,
      data: args.data,
      isRead: false,
      userId: args.userId || null,
      createdAt: Date.now(),
    });
    
    console.log(`Notification created: ${args.title} (${args.type})`);
    return notificationId;
  },
});

// Get recent email tracking notifications
export const getEmailTrackingNotifications = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    await getCurrentUser(ctx); // Ensure user is authenticated
    
    const limit = args.limit || 10;
    
    // Get recent email opened notifications
    const notifications = await ctx.db
      .query("notifications")
      .withIndex("by_type", (q) => q.eq("type", "email_opened"))
      .order("desc")
      .take(limit);
    
    return notifications;
  },
});

// Subscribe to real-time notifications
export const subscribeToNotifications = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    
    // Get recent notifications for real-time updates
    const userNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .take(5);
    
    const broadcastNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", null))
      .order("desc")
      .take(5);
    
    const allNotifications = [...userNotifications, ...broadcastNotifications]
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(0, 5);
    
    return {
      notifications: allNotifications,
      timestamp: Date.now(),
      userId,
    };
  },
});

// Clean up old notifications (internal)
export const cleanupOldNotifications = internalMutation({
  args: { olderThanDays: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const days = args.olderThanDays || 30;
    const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
    
    const oldNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_created_at", (q) => q.lt("createdAt", cutoffTime))
      .collect();
    
    let deletedCount = 0;
    for (const notification of oldNotifications) {
      await ctx.db.delete(notification._id);
      deletedCount++;
    }
    
    console.log(`Cleaned up ${deletedCount} old notifications older than ${days} days`);
    return { deletedCount };
  },
});

// Get notification statistics
export const getNotificationStats = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getCurrentUser(ctx);
    
    const now = Date.now();
    const oneDayAgo = now - (24 * 60 * 60 * 1000);
    const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);
    
    // Get user notifications
    const allUserNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();
    
    // Get broadcast notifications
    const allBroadcastNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", null))
      .collect();
    
    const allNotifications = [...allUserNotifications, ...allBroadcastNotifications];
    
    const stats = {
      total: allNotifications.length,
      unread: allNotifications.filter(n => !n.isRead).length,
      today: allNotifications.filter(n => n.createdAt > oneDayAgo).length,
      thisWeek: allNotifications.filter(n => n.createdAt > oneWeekAgo).length,
      emailOpened: allNotifications.filter(n => n.type === 'email_opened').length,
      byType: {} as Record<string, number>,
    };
    
    // Count by type
    allNotifications.forEach(notification => {
      stats.byType[notification.type] = (stats.byType[notification.type] || 0) + 1;
    });
    
    return stats;
  },
});
