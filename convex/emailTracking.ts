import { v } from "convex/values";
import { query, mutation, internalMutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";

// Get current user helper
async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

// Create email tracking record when email is sent
export const createEmailTracking = internalMutation({
  args: {
    emailId: v.string(),
    invoiceId: v.optional(v.id("invoices")),
    recipientEmail: v.string(),
    emailType: v.string(),
    trackingPixelUrl: v.string(),
    isTracked: v.boolean(),
    metadata: v.optional(v.any()),
    sentBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    // For internal mutations, we use the passed sentBy userId
    
    const trackingRecord = await ctx.db.insert("emailTracking", {
      emailId: args.emailId,
      invoiceId: args.invoiceId,
      recipientEmail: args.recipientEmail,
      emailType: args.emailType,
      sentAt: Date.now(),
      openCount: 0,
      trackingPixelUrl: args.trackingPixelUrl,
      isTracked: args.isTracked,
      metadata: args.metadata,
      sentBy: args.sentBy,
    });

    console.log(`Email tracking created for email ${args.emailId}`);
    return trackingRecord;
  },
});

// Record email open event (internal mutation for webhook)
export const recordEmailOpen = internalMutation({
  args: {
    emailId: v.string(),
    openedAt: v.number(),
    userAgent: v.optional(v.string()),
    ipAddress: v.optional(v.string()),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    // Find the email tracking record
    const trackingRecord = await ctx.db
      .query("emailTracking")
      .withIndex("by_email_id", (q) => q.eq("emailId", args.emailId))
      .first();

    if (!trackingRecord) {
      console.error(`No tracking record found for email ${args.emailId}`);
      return null;
    }

    // Anonymize IP address for privacy (keep first 3 octets for IPv4)
    const anonymizedIp = args.ipAddress ? 
      args.ipAddress.split('.').slice(0, 3).join('.') + '.xxx' : 
      undefined;

    // Update the tracking record
    const updatedRecord = await ctx.db.patch(trackingRecord._id, {
      openedAt: trackingRecord.openedAt || args.openedAt, // Keep first open time
      lastOpenedAt: args.openedAt,
      openCount: trackingRecord.openCount + 1,
      userAgent: args.userAgent,
      ipAddress: anonymizedIp,
      metadata: {
        ...trackingRecord.metadata,
        lastOpenMetadata: args.metadata,
      },
    });

    // Trigger notification for first open
    if (!trackingRecord.openedAt) {
      await ctx.runMutation(internal.emailTracking.triggerEmailOpenNotification, {
        trackingId: trackingRecord._id,
        emailId: args.emailId,
        invoiceId: trackingRecord.invoiceId,
        recipientEmail: trackingRecord.recipientEmail,
        emailType: trackingRecord.emailType,
        openedAt: args.openedAt,
      });
    }

    console.log(`Email ${args.emailId} opened. Total opens: ${trackingRecord.openCount + 1}`);
    return updatedRecord;
  },
});

// Record email open by tracking ID (for pixel tracking)
export const recordEmailOpenByTrackingId = internalMutation({
  args: {
    trackingId: v.string(),
    openedAt: v.number(),
    userAgent: v.optional(v.string()),
    ipAddress: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<any> => {
    // Find tracking record by pixel URL containing the tracking ID
    const allTrackingRecords = await ctx.db.query("emailTracking").collect();
    const trackingRecord = allTrackingRecords.find(record =>
      record.trackingPixelUrl.includes(args.trackingId)
    );

    if (!trackingRecord) {
      console.error(`No tracking record found for tracking ID ${args.trackingId}`);
      return null;
    }

    // Use the main recordEmailOpen function
    return await ctx.runMutation(internal.emailTracking.recordEmailOpen, {
      emailId: trackingRecord.emailId,
      openedAt: args.openedAt,
      userAgent: args.userAgent,
      ipAddress: args.ipAddress,
      metadata: { trackingMethod: 'pixel' },
    });
  },
});

// Trigger email open notification (internal)
export const triggerEmailOpenNotification = internalMutation({
  args: {
    trackingId: v.id("emailTracking"),
    emailId: v.string(),
    invoiceId: v.optional(v.id("invoices")),
    recipientEmail: v.string(),
    emailType: v.string(),
    openedAt: v.number(),
  },
  handler: async (ctx, args) => {
    // Get invoice details if available
    let invoiceDetails = null;
    if (args.invoiceId) {
      const invoice = await ctx.db.get(args.invoiceId);
      if (invoice) {
        const customer = await ctx.db.get(invoice.customerId);
        invoiceDetails = {
          invoiceNumber: invoice.invoiceNumber,
          customerName: customer?.name || 'Unknown Customer',
          total: invoice.total,
        };
      }
    }

    // Create notification data
    const notificationData = {
      type: 'email_opened',
      emailId: args.emailId,
      invoiceId: args.invoiceId,
      recipientEmail: args.recipientEmail,
      emailType: args.emailType,
      openedAt: args.openedAt,
      invoiceDetails,
      trackingId: args.trackingId,
    };

    // Store notification for real-time delivery using the notifications module
    await ctx.runMutation(internal.notifications.createNotification, {
      type: 'email_opened',
      title: invoiceDetails ?
        `Invoice ${invoiceDetails.invoiceNumber} email opened` :
        `${args.emailType} email opened`,
      message: invoiceDetails ?
        `${invoiceDetails.customerName} opened the invoice email for $${invoiceDetails.total.toFixed(2)}` :
        `Email sent to ${args.recipientEmail} has been opened`,
      data: notificationData,
      userId: null as any, // Broadcast to all users
    });

    console.log(`Email open notification triggered for ${args.emailId}`);
    return notificationData;
  },
});

// Get email tracking data for an invoice
export const getInvoiceEmailTracking = query({
  args: { invoiceId: v.id("invoices") },
  handler: async (ctx, args) => {
    await getCurrentUser(ctx); // Ensure user is authenticated
    
    const trackingRecords = await ctx.db
      .query("emailTracking")
      .withIndex("by_invoice", (q) => q.eq("invoiceId", args.invoiceId))
      .collect();

    return trackingRecords.map(record => ({
      ...record,
      // Don't expose sensitive data to frontend
      ipAddress: record.ipAddress ? 'xxx.xxx.xxx.xxx' : undefined,
      metadata: undefined,
    }));
  },
});

// Get email tracking analytics
export const getEmailTrackingAnalytics = query({
  args: {
    timeRange: v.optional(v.string()), // '24h', '7d', '30d'
    emailType: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await getCurrentUser(ctx); // Ensure user is authenticated
    
    const now = Date.now();
    const timeRanges = {
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
    };
    
    const timeRange = args.timeRange || '7d';
    const startTime = now - (timeRanges[timeRange as keyof typeof timeRanges] || timeRanges['7d']);
    
    let query = ctx.db
      .query("emailTracking")
      .withIndex("by_sent_at", (q) => q.gte("sentAt", startTime));
    
    if (args.emailType) {
      query = query.filter((q) => q.eq(q.field("emailType"), args.emailType));
    }
    
    const trackingRecords = await query.collect();
    
    const analytics = {
      totalSent: trackingRecords.length,
      totalOpened: trackingRecords.filter(r => r.openedAt).length,
      openRate: trackingRecords.length > 0 ? 
        (trackingRecords.filter(r => r.openedAt).length / trackingRecords.length * 100) : 0,
      totalOpens: trackingRecords.reduce((sum, r) => sum + r.openCount, 0),
      averageOpensPerEmail: trackingRecords.length > 0 ?
        trackingRecords.reduce((sum, r) => sum + r.openCount, 0) / trackingRecords.length : 0,
      recentOpens: trackingRecords
        .filter(r => r.lastOpenedAt && r.lastOpenedAt > now - (24 * 60 * 60 * 1000))
        .length,
    };
    
    return analytics;
  },
});

// Get recent email opens for notifications
export const getRecentEmailOpens = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    await getCurrentUser(ctx); // Ensure user is authenticated
    
    const limit = args.limit || 10;
    const recentOpens = await ctx.db
      .query("emailTracking")
      .withIndex("by_opened_at", (q) => q.gte("openedAt", 0))
      .order("desc")
      .take(limit);
    
    // Enrich with invoice data
    const enrichedOpens = await Promise.all(
      recentOpens.map(async (tracking) => {
        let invoiceData = null;
        if (tracking.invoiceId) {
          const invoice = await ctx.db.get(tracking.invoiceId);
          if (invoice) {
            const customer = await ctx.db.get(invoice.customerId);
            invoiceData = {
              invoiceNumber: invoice.invoiceNumber,
              customerName: customer?.name || 'Unknown Customer',
              total: invoice.total,
            };
          }
        }
        
        return {
          ...tracking,
          invoiceData,
          // Don't expose sensitive data
          ipAddress: undefined,
          metadata: undefined,
        };
      })
    );
    
    return enrichedOpens;
  },
});

// Generate unique tracking pixel URL
export function generateTrackingPixelUrl(emailId: string): string {
  // Create a unique tracking ID based on email ID and timestamp
  const trackingId = `${emailId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  return `/track/${trackingId}`;
}
