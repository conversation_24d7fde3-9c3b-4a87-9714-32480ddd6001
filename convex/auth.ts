import { convexAuth, getAuthUserId } from "@convex-dev/auth/server";
import { Password } from "@convex-dev/auth/providers/Password";
import { query, mutation } from "./_generated/server";
import { checkLoginRateLimit, recordAuthAttempt } from "./security/authRateLimit";

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [Password],
});

export const loggedInUser = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }
    const user = await ctx.db.get(userId);
    if (!user) {
      return null;
    }
    return user;
  },
});

// Permission helper functions
export async function getCurrentUser(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");
  return userId;
}

export async function getCurrentUserWithRole(ctx: any) {
  const userId = await getAuthUserId(ctx);
  if (!userId) throw new Error("Not authenticated");

  let user = await ctx.db.get(userId);
  if (!user) throw new Error("User not found");

  // Ensure user has a role assigned - if not, assign default "staff" role
  if (!user.role) {
    await ctx.db.patch(userId, { role: "staff" });
    user = { ...user, role: "staff" };
  }

  return {
    userId,
    role: user.role,
    user
  };
}

export async function checkAdminPermission(ctx: any, userId?: string) {
  const targetUserId = userId || await getCurrentUser(ctx);
  const user = await ctx.db.get(targetUserId);

  if (!user || (user.role !== "admin" && user.role !== "master")) {
    throw new Error("Unauthorized - Admin access required");
  }

  return user;
}

export async function checkMasterPermission(ctx: any, userId?: string) {
  const targetUserId = userId || await getCurrentUser(ctx);
  const user = await ctx.db.get(targetUserId);

  if (!user || user.role !== "master") {
    throw new Error("Unauthorized - Master access required");
  }

  return user;
}

export async function checkStaffOrAdminPermission(ctx: any, userId?: string) {
  const targetUserId = userId || await getCurrentUser(ctx);
  let user = await ctx.db.get(targetUserId);

  if (!user) {
    throw new Error("User not found");
  }

  // Ensure user has a role assigned - if not, assign default "staff" role
  let role = user.role;
  if (!role) {
    role = "staff";
    await ctx.db.patch(targetUserId, { role: "staff" });
    user = { ...user, role: "staff" };
  }

  if (role !== "staff" && role !== "admin" && role !== "master") {
    throw new Error("Unauthorized - Staff, Admin, or Master access required");
  }

  return { user, role };
}

// Read-only version for queries (cannot patch database)
export async function checkStaffOrAdminPermissionReadOnly(ctx: any, userId?: string) {
  const targetUserId = userId || await getCurrentUser(ctx);
  let user = await ctx.db.get(targetUserId);

  if (!user) {
    throw new Error("User not found");
  }

  // For queries, we assume users without roles are staff (but don't patch)
  let role = user.role || "staff";

  if (role !== "staff" && role !== "admin" && role !== "master") {
    throw new Error("Unauthorized - Staff, Admin, or Master access required");
  }

  return { user: { ...user, role }, role };
}

export function canDeleteRecord(userRole: string): boolean {
  return userRole === "admin" || userRole === "master";
}

// Role hierarchy helper functions
export function getRoleLevel(role: string): number {
  switch (role) {
    case "master": return 3;
    case "admin": return 2;
    case "staff": return 1;
    default: return 0;
  }
}

export function canAssignRole(currentUserRole: string, targetRole: string): boolean {
  const currentLevel = getRoleLevel(currentUserRole);
  const targetLevel = getRoleLevel(targetRole);

  // Only Master can assign any role
  if (currentUserRole === "master") {
    return true;
  }

  // Admin cannot assign admin or master roles
  if (currentUserRole === "admin") {
    return targetRole === "staff";
  }

  // Staff cannot assign any roles
  return false;
}

export function canManageUser(currentUserRole: string, targetUserRole: string): boolean {
  const currentLevel = getRoleLevel(currentUserRole);
  const targetLevel = getRoleLevel(targetUserRole);

  // Master can manage anyone
  if (currentUserRole === "master") {
    return true;
  }

  // Admin can manage staff but not other admins or masters
  if (currentUserRole === "admin") {
    return targetUserRole === "staff";
  }

  // Staff cannot manage anyone
  return false;
}

export function canAccessAllData(userRole: string): boolean {
  return userRole === "staff" || userRole === "admin" || userRole === "master";
}