import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import {
  getCurrentUserWithRole,
  checkAdminPermission,
  checkStaffOrAdminPermission,
  checkStaffOrAdminPermissionReadOnly,
  canDeleteRecord
} from "./auth";

// List all jobs (staff and admin can see all jobs)
export const list = query({
  args: {},
  handler: async (ctx) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermissionReadOnly(ctx);

    // Staff and admin users can see all jobs
    const jobs = await ctx.db
      .query("jobs")
      .order("desc")
      .collect();

    // Get customer info for each job
    const jobsWithCustomers = await Promise.all(
      jobs.map(async (job) => {
        const customer = await ctx.db.get(job.customerId);
        return {
          ...job,
          customer: customer ? { name: customer.name, email: customer.email } : null,
        };
      })
    );

    return jobsWithCustomers;
  },
});

// Get jobs by status (staff and admin can see all jobs)
export const getByStatus = query({
  args: { status: v.string() },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermissionReadOnly(ctx);

    // Staff and admin users can see all jobs by status
    const jobs = await ctx.db
      .query("jobs")
      .withIndex("by_status", (q) => q.eq("status", args.status))
      .collect();

    const jobsWithCustomers = await Promise.all(
      jobs.map(async (job) => {
        const customer = await ctx.db.get(job.customerId);
        return {
          ...job,
          customer: customer ? { name: customer.name, email: customer.email } : null,
        };
      })
    );

    return jobsWithCustomers;
  },
});

// Get a single job by ID (staff and admin can access any job)
export const get = query({
  args: { id: v.id("jobs") },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermissionReadOnly(ctx);

    const job = await ctx.db.get(args.id);
    if (!job) {
      throw new Error("Job not found");
    }

    const customer = await ctx.db.get(job.customerId);
    return {
      ...job,
      customer,
    };
  },
});

// Get jobs by customer ID (for invoice job selection)
export const getByCustomer = query({
  args: { customerId: v.id("customers") },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermissionReadOnly(ctx);

    const jobs = await ctx.db
      .query("jobs")
      .withIndex("by_customer", (q) => q.eq("customerId", args.customerId))
      .order("desc")
      .collect();

    return jobs;
  },
});

// Get jobs in progress with full customer location data for map display
export const getInProgressWithLocations = query({
  args: {},
  handler: async (ctx) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermissionReadOnly(ctx);

    const jobs = await ctx.db
      .query("jobs")
      .withIndex("by_status", (q) => q.eq("status", "in-progress"))
      .collect();

    // Get full customer data including location info
    const jobsWithFullCustomers = await Promise.all(
      jobs.map(async (job) => {
        const customer = await ctx.db.get(job.customerId);
        const assignedUser = await ctx.db.get(job.assignedTo);
        
        return {
          ...job,
          customer,
          assignedUser: assignedUser ? { name: assignedUser.name, email: assignedUser.email } : null,
        };
      })
    );

    // Filter out jobs where customer data is missing or incomplete
    return jobsWithFullCustomers.filter(job => 
      job.customer && 
      job.customer.address && 
      job.customer.city && 
      job.customer.state
    );
  },
});

// Create a new job (staff and admin can create jobs)
export const create = mutation({
  args: {
    customerId: v.id("customers"),
    title: v.string(),
    description: v.string(),
    status: v.string(),
    priority: v.string(),
    scheduledDate: v.optional(v.number()),
    completedDate: v.optional(v.number()),
    estimatedHours: v.optional(v.number()),
    notes: v.optional(v.string()),
    assignedTo: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    const { user } = await checkStaffOrAdminPermission(ctx);

    return await ctx.db.insert("jobs", {
      ...args,
      createdBy: user._id,
    });
  },
});

// Update an existing job (staff and admin can update any job)
export const update = mutation({
  args: {
    id: v.id("jobs"),
    customerId: v.id("customers"),
    title: v.string(),
    description: v.string(),
    status: v.string(),
    priority: v.string(),
    scheduledDate: v.optional(v.number()),
    completedDate: v.optional(v.number()),
    estimatedHours: v.optional(v.number()),
    actualHours: v.optional(v.number()),
    notes: v.optional(v.string()),
    assignedTo: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    const { id, ...updates } = args;

    const job = await ctx.db.get(id);
    if (!job) {
      throw new Error("Job not found");
    }

    await ctx.db.patch(id, updates);
  },
});

// Delete a job (admin only)
export const remove = mutation({
  args: { id: v.id("jobs") },
  handler: async (ctx, args) => {
    // Check if user has admin permissions (only admins can delete)
    await checkAdminPermission(ctx);

    const job = await ctx.db.get(args.id);
    if (!job) {
      throw new Error("Job not found");
    }
    await ctx.db.delete(args.id);
  },
});

// Get recent jobs for dashboard (staff and admin can see all recent jobs)
export const getRecent = query({
  args: {},
  handler: async (ctx) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermissionReadOnly(ctx);

    // Staff and admin users can see all recent jobs
    const jobs = await ctx.db
      .query("jobs")
      .order("desc")
      .take(10);

    // Get customer info for each job
    const jobsWithCustomers = await Promise.all(
      jobs.map(async (job) => {
        const customer = await ctx.db.get(job.customerId);
        return {
          ...job,
          customer: customer ? { name: customer.name, email: customer.email } : null,
        };
      })
    );

    return jobsWithCustomers;
  },
});
