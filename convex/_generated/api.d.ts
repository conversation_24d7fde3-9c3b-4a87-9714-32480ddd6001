/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as admin_rateLimitAdmin from "../admin/rateLimitAdmin.js";
import type * as auth from "../auth.js";
import type * as customers from "../customers.js";
import type * as dashboard from "../dashboard.js";
import type * as email_pdfService from "../email/pdfService.js";
import type * as email_pdfTemplates from "../email/pdfTemplates.js";
import type * as email_service from "../email/service.js";
import type * as email_templates from "../email/templates.js";
import type * as email_validation from "../email/validation.js";
import type * as emailTemplates from "../emailTemplates.js";
import type * as emailTracking from "../emailTracking.js";
import type * as http from "../http.js";
import type * as invoiceEmail from "../invoiceEmail.js";
import type * as invoicePDFEmail from "../invoicePDFEmail.js";
import type * as invoices from "../invoices.js";
import type * as jobs from "../jobs.js";
import type * as migrateCustomers from "../migrateCustomers.js";
import type * as notifications from "../notifications.js";
import type * as products from "../products.js";
import type * as roleManagement from "../roleManagement.js";
import type * as router from "../router.js";
import type * as search from "../search.js";
import type * as security_authRateLimit from "../security/authRateLimit.js";
import type * as security_encryption from "../security/encryption.js";
import type * as security_inputValidation from "../security/inputValidation.js";
import type * as security_migration from "../security/migration.js";
import type * as security_monitoring from "../security/monitoring.js";
import type * as security_rateLimiting from "../security/rateLimiting.js";
import type * as security_securityValidation from "../security/securityValidation.js";
import type * as settings from "../settings.js";
import type * as sms_errorHandling from "../sms/errorHandling.js";
import type * as sms_security from "../sms/security.js";
import type * as sms_service from "../sms/service.js";
import type * as sms_validation from "../sms/validation.js";
import type * as sms from "../sms.js";
import type * as smsTemplates from "../smsTemplates.js";
import type * as twilioConfig from "../twilioConfig.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  "admin/rateLimitAdmin": typeof admin_rateLimitAdmin;
  auth: typeof auth;
  customers: typeof customers;
  dashboard: typeof dashboard;
  "email/pdfService": typeof email_pdfService;
  "email/pdfTemplates": typeof email_pdfTemplates;
  "email/service": typeof email_service;
  "email/templates": typeof email_templates;
  "email/validation": typeof email_validation;
  emailTemplates: typeof emailTemplates;
  emailTracking: typeof emailTracking;
  http: typeof http;
  invoiceEmail: typeof invoiceEmail;
  invoicePDFEmail: typeof invoicePDFEmail;
  invoices: typeof invoices;
  jobs: typeof jobs;
  migrateCustomers: typeof migrateCustomers;
  notifications: typeof notifications;
  products: typeof products;
  roleManagement: typeof roleManagement;
  router: typeof router;
  search: typeof search;
  "security/authRateLimit": typeof security_authRateLimit;
  "security/encryption": typeof security_encryption;
  "security/inputValidation": typeof security_inputValidation;
  "security/migration": typeof security_migration;
  "security/monitoring": typeof security_monitoring;
  "security/rateLimiting": typeof security_rateLimiting;
  "security/securityValidation": typeof security_securityValidation;
  settings: typeof settings;
  "sms/errorHandling": typeof sms_errorHandling;
  "sms/security": typeof sms_security;
  "sms/service": typeof sms_service;
  "sms/validation": typeof sms_validation;
  sms: typeof sms;
  smsTemplates: typeof smsTemplates;
  twilioConfig: typeof twilioConfig;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
