import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import {
  getCurrentUserWithRole,
  checkAdminPermission,
  checkStaffOrAdminPermission,
  checkStaffOrAdminPermissionReadOnly,
  canDeleteRecord
} from "./auth";
import { validateCustomerData, sanitizeString } from "./security/inputValidation";

// List all customers (staff and admin can see all customers)
export const list = query({
  args: {},
  handler: async (ctx) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermissionReadOnly(ctx);

    // Staff and admin users can see all customers
    return await ctx.db
      .query("customers")
      .order("desc")
      .collect();
  },
});

// Get a single customer by ID (staff and admin can access any customer)
export const get = query({
  args: { id: v.id("customers") },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermissionReadOnly(ctx);

    const customer = await ctx.db.get(args.id);
    if (!customer) {
      throw new Error("Customer not found");
    }
    return customer;
  },
});

// Create a new customer (staff and admin can create customers)
export const create = mutation({
  args: {
    name: v.string(),
    email: v.string(),
    phone: v.string(),
    address: v.string(),
    city: v.string(),
    state: v.string(),
    zipCode: v.string(),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    const { user } = await checkStaffOrAdminPermission(ctx);

    // Validate and sanitize input data
    const validation = validateCustomerData(args);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Log warnings if any
    if (validation.warnings.length > 0) {
      console.warn('Customer creation warnings:', validation.warnings);
    }

    // Sanitize individual fields
    const sanitizedData = {
      name: sanitizeString(args.name, { required: true, maxLength: 100 }).sanitized || '',
      email: sanitizeString(args.email, { required: true, maxLength: 255 }).sanitized?.toLowerCase() || '',
      phone: sanitizeString(args.phone, { required: true, maxLength: 20 }).sanitized || '',
      address: sanitizeString(args.address, { required: true, maxLength: 500 }).sanitized || '',
      city: sanitizeString(args.city, { required: true, maxLength: 100 }).sanitized || '',
      state: sanitizeString(args.state, { required: true, maxLength: 50 }).sanitized || '',
      zipCode: sanitizeString(args.zipCode, { required: true, maxLength: 20 }).sanitized || '',
      notes: args.notes ? sanitizeString(args.notes, { maxLength: 2000 }).sanitized : undefined,
    };

    const now = Date.now();
    return await ctx.db.insert("customers", {
      ...sanitizedData,
      createdBy: user._id,
      status: "active", // Default status
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Update an existing customer (staff and admin can update any customer)
export const update = mutation({
  args: {
    id: v.id("customers"),
    name: v.string(),
    email: v.string(),
    phone: v.string(),
    address: v.string(),
    city: v.string(),
    state: v.string(),
    zipCode: v.string(),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermission(ctx);

    const { id, ...updateData } = args;

    const customer = await ctx.db.get(id);
    if (!customer) {
      throw new Error("Customer not found");
    }

    // Validate and sanitize input data
    const validation = validateCustomerData(updateData);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Log warnings if any
    if (validation.warnings.length > 0) {
      console.warn('Customer update warnings:', validation.warnings);
    }

    // Sanitize individual fields
    const sanitizedUpdates = {
      name: sanitizeString(updateData.name, { required: true, maxLength: 100 }).sanitized || '',
      email: sanitizeString(updateData.email, { required: true, maxLength: 255 }).sanitized?.toLowerCase() || '',
      phone: sanitizeString(updateData.phone, { required: true, maxLength: 20 }).sanitized || '',
      address: sanitizeString(updateData.address, { required: true, maxLength: 500 }).sanitized || '',
      city: sanitizeString(updateData.city, { required: true, maxLength: 100 }).sanitized || '',
      state: sanitizeString(updateData.state, { required: true, maxLength: 50 }).sanitized || '',
      zipCode: sanitizeString(updateData.zipCode, { required: true, maxLength: 20 }).sanitized || '',
      notes: updateData.notes ? sanitizeString(updateData.notes, { maxLength: 2000 }).sanitized : undefined,
    };

    await ctx.db.patch(id, {
      ...sanitizedUpdates,
      updatedAt: Date.now(),
    });
  },
});

// Delete a customer (admin only)
export const remove = mutation({
  args: { id: v.id("customers") },
  handler: async (ctx, args) => {
    // Check if user has admin permissions (only admins can delete)
    await checkAdminPermission(ctx);

    const customer = await ctx.db.get(args.id);
    if (!customer) {
      throw new Error("Customer not found");
    }
    await ctx.db.delete(args.id);
  },
});

// Search customers by name or email (staff and admin can search all customers)
export const search = query({
  args: { query: v.string() },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermissionReadOnly(ctx);

    // Get all customers (staff and admin can search all)
    const customers = await ctx.db
      .query("customers")
      .collect();

    const searchTerm = args.query.toLowerCase();
    return customers.filter(
      (customer) =>
        customer.name.toLowerCase().includes(searchTerm) ||
        customer.email.toLowerCase().includes(searchTerm)
    );
  },
});

// Import customers from CSV-formatted data
export const bulkImport = mutation({
  args: {
    customers: v.array(
      v.object({
        id: v.optional(v.string()),
        name: v.string(),
        email: v.string(),
        phone: v.string(),
        company: v.optional(v.string()),
        industry: v.optional(v.string()),
        address: v.string(),
        city: v.string(),
        state: v.string(),
        zipCode: v.string(),
        status: v.optional(v.string()),
        notes: v.optional(v.string()),
        county: v.optional(v.string()),
        createdAt: v.optional(v.string()),
        updatedAt: v.optional(v.string()),
        additionalFields: v.optional(v.any()), // Added to allow passing through additional fields
      })
    ),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    const { user } = await checkStaffOrAdminPermission(ctx);
    const results = {
      imported: 0,
      skipped: 0,
      updated: 0,
      errors: [] as string[],
      newFields: new Set<string>(), // Track any new fields encountered
    };
    
    // Process each customer in the import
    for (const customerData of args.customers) {
      try {
        // Normalize email to lowercase for comparison, or use a placeholder if empty
        const normalizedEmail = customerData.email && customerData.email.trim()
          ? customerData.email.toLowerCase().trim()
          : `no-email-${Date.now()}-${Math.random().toString(36).substring(2, 15)}@placeholder.com`;

        // If additionalFields exists, track any new field names
        if (customerData.additionalFields && typeof customerData.additionalFields === 'object') {
          Object.keys(customerData.additionalFields).forEach(field => {
            results.newFields.add(field);
          });
        }

        const now = Date.now();
        
        // Prepare the customer data for insertion
        const customerRecord = {
          name: customerData.name,
          email: normalizedEmail, // Store normalized or generated email
          phone: customerData.phone || "",
          address: customerData.address || "",
          city: customerData.city || "",
          state: customerData.state || "",
          zipCode: customerData.zipCode || "",
          notes: customerData.notes || "",
          company: customerData.company,
          industry: customerData.industry,
          county: customerData.county,
          status: customerData.status || "active", // Default status
          createdAt: now,
          updatedAt: now,
          createdBy: user._id,
    };

        // Always insert as a new customer
        await ctx.db.insert("customers", customerRecord);
        results.imported++;
      } catch (error) {
        results.errors.push(`Error with ${customerData.name}: ${error}`);
        results.skipped++;
      }
    }
    
    return {
      ...results,
      newFields: Array.from(results.newFields), // Convert Set to Array for JSON response
    };
  },
});

// Check import status and return missing customers
export const checkImport = query({
  args: {
    customers: v.array(
      v.object({
        id: v.optional(v.string()),
        name: v.string(),
        email: v.string(),
      })
    ),
  },
  handler: async (ctx, args) => {
    // Check if user has staff or admin permissions
    await checkStaffOrAdminPermissionReadOnly(ctx);

    const missingCustomers = [];
    const existingEmails = new Set();

    // Get all existing customer emails (staff and admin can see all)
    const existingCustomers = await ctx.db
      .query("customers")
      .collect();
    
    existingCustomers.forEach(customer => {
      existingEmails.add(customer.email.toLowerCase().trim());
    });
    
    // Check which customers from the import are missing
    for (const customer of args.customers) {
      if (!customer.email || !customer.email.trim()) {
        // Skip customers with empty emails
        continue;
      }

      const normalizedEmail = customer.email.toLowerCase().trim();
      if (!existingEmails.has(normalizedEmail)) {
        missingCustomers.push({
          ...customer,
          email: normalizedEmail // Normalize the email
        });
      }
    }
    
    return {
      existingCount: existingCustomers.length,
      missingCount: missingCustomers.length,
      missingCustomers: missingCustomers,
    };
  },
});